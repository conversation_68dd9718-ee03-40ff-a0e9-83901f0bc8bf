from endstone.plugin import Plugin
from endstone.event import event_handler, PlayerInteractActorEvent
from endstone.form import ModalForm, Dropdown, TextInput, ActionForm
from endstone import ColorFormat, Player
from endstone.scoreboard import Criteria, DisplaySlot, ObjectiveSortOrder
from endstone.command import Command, CommandSender
import json
import os
from typing import Dict, List, Any


class OGCoins(Plugin):
    api_version = "0.10"

    MONEY_OBJECTIVE = "Money"
    CONFIG_FILE = "currency_config.json"
    MESSAGES_FILE = "messages_config.json"

    commands = {
        "currency": {
            "description": "Manage withdrawal currencies",
            "usages": ["/currency <add|remove|edit|list|reload>"],
            "permissions": ["ogcoins.admin"],
        },
        "messages": {
            "description": "Manage system messages",
            "usages": ["/messages <edit|reload|reset>"],
            "permissions": ["ogcoins.admin"],
        }
    }

    permissions = {
        "ogcoins.admin": {
            "description": "Allow users to manage OG Coins system.",
            "default": "op",
        }
    }

    def __init__(self):
        super().__init__()
        self.coin_config = []
        self.config_path = ""
        self.messages_config = {}
        self.messages_path = ""

    def _get_default_currency_config(self) -> List[Dict[str, Any]]:
        """Get the default currency configuration"""
        return [
            {"name": "ninjos:coin_netherite", "points": 1000000,
             "display_name": f"{ColorFormat.DARK_GRAY}Netherite TK Coin {ColorFormat.WHITE}$1,000,000"},
            {"name": "ninjos:coin_diamond", "points": 10000,
             "display_name": f"{ColorFormat.AQUA}Diamond TK Coin {ColorFormat.WHITE}$10,000"},
            {"name": "ninjos:coin_gold", "points": 1000,
             "display_name": f"{ColorFormat.YELLOW}Golden TK Coin {ColorFormat.WHITE}$1,000"},
            {"name": "ninjos:coin_ruby", "points": 500,
             "display_name": f"{ColorFormat.RED}Ruby TK Coin {ColorFormat.WHITE}$500"},
            {"name": "ninjos:coin_emerald", "points": 100,
             "display_name": f"{ColorFormat.DARK_GREEN}Emerald TK Coin {ColorFormat.WHITE}$100"},
            {"name": "ninjos:coin_iron", "points": 25,
             "display_name": f"{ColorFormat.DARK_GRAY}Iron TK Coin {ColorFormat.WHITE}$25"},
            {"name": "ninjos:coin_glass", "points": 5,
             "display_name": f"{ColorFormat.GRAY}Glass TK Coin {ColorFormat.WHITE}$5"},
            {"name": "ninjos:coin_wood", "points": 1,
             "display_name": f"{ColorFormat.GOLD}Wooden TK Coin {ColorFormat.WHITE}$1"}
        ]

    def _load_currency_config(self) -> None:
        """Load currency configuration from file"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                if self._validate_currency_config(config_data):
                    self.coin_config = config_data
                    self.logger.info(f"Loaded {len(self.coin_config)} currencies from config file")
                else:
                    self.logger.warning("Invalid currency config, using defaults")
                    self.coin_config = self._get_default_currency_config()
                    self._save_currency_config()
            else:
                self.logger.info("No currency config found, creating default configuration")
                self.coin_config = self._get_default_currency_config()
                self._save_currency_config()

            # Sort by points (highest to lowest) to ensure proper distribution
            self.coin_config.sort(key=lambda x: x["points"], reverse=True)

        except Exception as e:
            self.logger.error(f"Error loading currency config: {e}")
            self.coin_config = self._get_default_currency_config()

    def _save_currency_config(self) -> bool:
        """Save currency configuration to file"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.coin_config, f, indent=2, ensure_ascii=False)
            self.logger.info("Currency configuration saved successfully")
            return True
        except Exception as e:
            self.logger.error(f"Error saving currency config: {e}")
            return False

    def _validate_currency_config(self, config: List[Dict[str, Any]]) -> bool:
        """Validate currency configuration"""
        if not isinstance(config, list):
            return False

        for currency in config:
            if not isinstance(currency, dict):
                return False
            if not all(key in currency for key in ["name", "points", "display_name"]):
                return False
            if not isinstance(currency["name"], str) or not currency["name"]:
                return False
            if not isinstance(currency["points"], int) or currency["points"] <= 0:
                return False
            if not isinstance(currency["display_name"], str) or not currency["display_name"]:
                return False

        return True

    def _get_default_messages_config(self) -> Dict[str, str]:
        """Get the default messages configuration"""
        return {
            "currency_name": "TK Coins",
            "currency_name_singular": "TK Coin",
            "withdrawal_title": "Money Withdrawal",
            "transfer_title": "Money Transfer",
            "withdrawal_insufficient": "You don't have enough {currency_name} to withdraw!",
            "transfer_insufficient": "You don't have enough {currency_name} to transfer!",
            "withdrawal_prompt": "How Much Would You Like To Withdraw?",
            "transfer_prompt": "Select a player and amount to transfer:",
            "withdrawal_success": "You successfully withdrew:",
            "transfer_success": "You successfully transferred ${amount} to {player}!",
            "transfer_received": "You received ${amount} from {player}!",
            "withdrawal_canceled": "Withdrawal canceled!",
            "transfer_canceled": "Transfer canceled!",
            "invalid_amount": "Only numbers are allowed!",
            "amount_too_low": "Amount must be greater than 0!",
            "amount_too_high": "You don't have enough money!",
            "inventory_full": "Your inventory can only hold {max_amount} worth of coins. Withdrawing maximum possible amount.",
            "player_not_found": "Player not found!",
            "cannot_transfer_self": "You cannot transfer money to yourself!",
            "error_processing": "Error processing transaction!"
        }

    def _load_messages_config(self) -> None:
        """Load messages configuration from file"""
        try:
            if os.path.exists(self.messages_path):
                with open(self.messages_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                if self._validate_messages_config(config_data):
                    self.messages_config = config_data
                    self.logger.info(f"Loaded messages configuration from file")
                else:
                    self.logger.warning("Invalid messages config, using defaults")
                    self.messages_config = self._get_default_messages_config()
                    self._save_messages_config()
            else:
                self.logger.info("No messages config found, creating default configuration")
                self.messages_config = self._get_default_messages_config()
                self._save_messages_config()

        except Exception as e:
            self.logger.error(f"Error loading messages config: {e}")
            self.messages_config = self._get_default_messages_config()

    def _save_messages_config(self) -> bool:
        """Save messages configuration to file"""
        try:
            with open(self.messages_path, 'w', encoding='utf-8') as f:
                json.dump(self.messages_config, f, indent=2, ensure_ascii=False)
            self.logger.info("Messages configuration saved successfully")
            return True
        except Exception as e:
            self.logger.error(f"Error saving messages config: {e}")
            return False

    def _validate_messages_config(self, config: Dict[str, str]) -> bool:
        """Validate messages configuration"""
        if not isinstance(config, dict):
            return False

        required_keys = self._get_default_messages_config().keys()
        return all(key in config and isinstance(config[key], str) for key in required_keys)

    def get_message(self, key: str, **kwargs) -> str:
        """Get a formatted message from configuration"""
        message = self.messages_config.get(key, f"Missing message: {key}")
        try:
            return message.format(**kwargs)
        except KeyError as e:
            self.logger.warning(f"Missing format parameter {e} for message '{key}'")
            return message

    def on_load(self) -> None:
        """Called when plugin is loaded"""
        self.logger.info("=" * 50)
        self.logger.info("TK Coins Plugin Loading...")
        self.logger.info("=" * 50)

        # Set up configuration paths
        self.config_path = os.path.join(self.data_folder, self.CONFIG_FILE)
        self.messages_path = os.path.join(self.data_folder, self.MESSAGES_FILE)

        # Create data folder if it doesn't exist
        os.makedirs(self.data_folder, exist_ok=True)

        # Load configurations
        self._load_currency_config()
        self._load_messages_config()

        # Commands are registered automatically via the commands class attribute
        self.logger.info("Commands registered: /currency, /messages")

    def on_command(self, sender: CommandSender, command: Command, args: list[str]) -> bool:
        """Handle plugin commands"""
        if command.name == "currency":
            return self._handle_currency_command(sender, command, args)
        elif command.name == "messages":
            return self._handle_messages_command(sender, command, args)
        return False

    def _handle_currency_command(self, sender: CommandSender, command: Command, args: list[str]) -> bool:
        """Handle currency management commands"""
        if not isinstance(sender, Player):
            sender.send_message(f"{ColorFormat.RED}This command can only be used by players!")
            return True

        if not args:
            self._show_currency_help(sender)
            return True

        action = args[0].lower()

        if action == "list":
            self._show_currency_list(sender)
        elif action == "add":
            self._show_add_currency_form(sender)
        elif action == "remove":
            if len(args) < 2:
                sender.send_message(f"{ColorFormat.RED}Usage: /currency remove <currency_name>")
            else:
                self._remove_currency(sender, args[1])
        elif action == "edit":
            if len(args) < 2:
                sender.send_message(f"{ColorFormat.RED}Usage: /currency edit <currency_name>")
            else:
                self._show_edit_currency_form(sender, args[1])
        elif action == "reload":
            self._reload_currency_config(sender)
        else:
            self._show_currency_help(sender)

        return True

    def _handle_messages_command(self, sender: CommandSender, command: Command, args: list[str]) -> bool:
        """Handle messages management commands"""
        if not isinstance(sender, Player):
            sender.send_message(f"{ColorFormat.RED}This command can only be used by players!")
            return True

        if not args:
            self._show_messages_help(sender)
            return True

        action = args[0].lower()

        if action == "edit":
            self._show_edit_messages_form(sender)
        elif action == "reload":
            self._reload_messages_config(sender)
        elif action == "reset":
            self._reset_messages_config(sender)
        else:
            self._show_messages_help(sender)

        return True

    def _show_currency_help(self, player: Player) -> None:
        """Show currency command help"""
        player.send_message(f"{ColorFormat.YELLOW}=== Currency Management Commands ===")
        player.send_message(f"{ColorFormat.GREEN}/currency list {ColorFormat.WHITE}- List all currencies")
        player.send_message(f"{ColorFormat.GREEN}/currency add {ColorFormat.WHITE}- Add a new currency")
        player.send_message(f"{ColorFormat.GREEN}/currency remove <name> {ColorFormat.WHITE}- Remove a currency")
        player.send_message(f"{ColorFormat.GREEN}/currency edit <name> {ColorFormat.WHITE}- Edit a currency")
        player.send_message(f"{ColorFormat.GREEN}/currency reload {ColorFormat.WHITE}- Reload config from file")

    def _show_currency_list(self, player: Player) -> None:
        """Show list of all currencies"""
        player.send_message(f"{ColorFormat.YELLOW}=== Current Currencies ({len(self.coin_config)}) ===")
        for i, currency in enumerate(self.coin_config, 1):
            player.send_message(f"{ColorFormat.GREEN}{i}. {ColorFormat.WHITE}{currency['name']} "
                              f"{ColorFormat.YELLOW}(${currency['points']:,}) {ColorFormat.RESET}- {currency['display_name']}")

    def _reload_currency_config(self, player: Player) -> None:
        """Reload currency configuration"""
        try:
            self._load_currency_config()
            player.send_message(f"{ColorFormat.GREEN}Currency configuration reloaded successfully!")
            player.send_message(f"{ColorFormat.YELLOW}Loaded {len(self.coin_config)} currencies")
        except Exception as e:
            player.send_message(f"{ColorFormat.RED}Error reloading configuration: {e}")

    def _remove_currency(self, player: Player, currency_name: str) -> None:
        """Remove a currency"""
        original_count = len(self.coin_config)
        self.coin_config = [c for c in self.coin_config if c["name"] != currency_name]

        if len(self.coin_config) < original_count:
            if self._save_currency_config():
                player.send_message(f"{ColorFormat.GREEN}Currency '{currency_name}' removed successfully!")
            else:
                player.send_message(f"{ColorFormat.RED}Error saving configuration!")
        else:
            player.send_message(f"{ColorFormat.RED}Currency '{currency_name}' not found!")

    def _show_messages_help(self, player: Player) -> None:
        """Show messages command help"""
        player.send_message(f"{ColorFormat.YELLOW}=== Messages Management Commands ===")
        player.send_message(f"{ColorFormat.GREEN}/messages edit {ColorFormat.WHITE}- Edit system messages")
        player.send_message(f"{ColorFormat.GREEN}/messages reload {ColorFormat.WHITE}- Reload messages from file")
        player.send_message(f"{ColorFormat.GREEN}/messages reset {ColorFormat.WHITE}- Reset to default messages")

    def _show_edit_messages_form(self, player: Player) -> None:
        """Show form to edit messages"""
        form = ModalForm(title=f"{ColorFormat.GREEN}Edit System Messages")

        form.add_control(TextInput(
            label="Currency Name (plural, e.g., 'TK Coins')",
            placeholder="TK Coins",
            default_value=self.messages_config.get("currency_name", "TK Coins")
        ))

        form.add_control(TextInput(
            label="Currency Name (singular, e.g., 'TK Coin')",
            placeholder="TK Coin",
            default_value=self.messages_config.get("currency_name_singular", "TK Coin")
        ))

        form.add_control(TextInput(
            label="Withdrawal Title",
            placeholder="Money Withdrawal",
            default_value=self.messages_config.get("withdrawal_title", "Money Withdrawal")
        ))

        form.add_control(TextInput(
            label="Transfer Title",
            placeholder="Money Transfer",
            default_value=self.messages_config.get("transfer_title", "Money Transfer")
        ))

        def on_submit(submitter, data: str) -> None:
            self._process_edit_messages(submitter, data)

        def on_close(submitter) -> None:
            submitter.send_message(f"{ColorFormat.YELLOW}Messages edit canceled")

        form.on_submit = on_submit
        form.on_close = on_close
        player.send_form(form)

    def _process_edit_messages(self, player: Player, data: str) -> None:
        """Process editing messages"""
        try:
            import json
            form_data = json.loads(data)

            if len(form_data) < 4:
                player.send_message(f"{ColorFormat.RED}Invalid form data!")
                return

            currency_name = form_data[0].strip()
            currency_name_singular = form_data[1].strip()
            withdrawal_title = form_data[2].strip()
            transfer_title = form_data[3].strip()

            # Validate input
            if not currency_name or not currency_name_singular:
                player.send_message(f"{ColorFormat.RED}Currency names cannot be empty!")
                return

            if not withdrawal_title or not transfer_title:
                player.send_message(f"{ColorFormat.RED}Titles cannot be empty!")
                return

            # Update messages
            self.messages_config["currency_name"] = currency_name
            self.messages_config["currency_name_singular"] = currency_name_singular
            self.messages_config["withdrawal_title"] = withdrawal_title
            self.messages_config["transfer_title"] = transfer_title

            # Update related messages with new currency name
            self.messages_config["withdrawal_insufficient"] = f"You don't have enough {currency_name} to withdraw!"
            self.messages_config["transfer_insufficient"] = f"You don't have enough {currency_name} to transfer!"

            if self._save_messages_config():
                player.send_message(f"{ColorFormat.GREEN}Messages updated successfully!")
                player.send_message(f"{ColorFormat.YELLOW}Currency name: {currency_name}")
                player.send_message(f"{ColorFormat.YELLOW}Withdrawal title: {withdrawal_title}")
                player.send_message(f"{ColorFormat.YELLOW}Transfer title: {transfer_title}")
            else:
                player.send_message(f"{ColorFormat.RED}Error saving messages configuration!")

        except Exception as e:
            player.send_message(f"{ColorFormat.RED}Error updating messages: {e}")

    def _reload_messages_config(self, player: Player) -> None:
        """Reload messages configuration"""
        try:
            self._load_messages_config()
            player.send_message(f"{ColorFormat.GREEN}Messages configuration reloaded successfully!")
        except Exception as e:
            player.send_message(f"{ColorFormat.RED}Error reloading messages: {e}")

    def _reset_messages_config(self, player: Player) -> None:
        """Reset messages to defaults"""
        try:
            self.messages_config = self._get_default_messages_config()
            if self._save_messages_config():
                player.send_message(f"{ColorFormat.GREEN}Messages reset to defaults successfully!")
            else:
                player.send_message(f"{ColorFormat.RED}Error saving default messages!")
        except Exception as e:
            player.send_message(f"{ColorFormat.RED}Error resetting messages: {e}")

    def _show_add_currency_form(self, player: Player) -> None:
        """Show form to add a new currency"""
        form = ModalForm(title=f"{ColorFormat.GREEN}Add New Currency")

        form.add_control(TextInput(
            label="Item Name (e.g., minecraft:diamond)",
            placeholder="namespace:item_name"
        ))

        form.add_control(TextInput(
            label="Point Value",
            placeholder="100"
        ))

        form.add_control(TextInput(
            label="Display Name",
            placeholder="Diamond Coin $100"
        ))

        def on_submit(submitter, data: str) -> None:
            self._process_add_currency(submitter, data)

        def on_close(submitter) -> None:
            submitter.send_message(f"{ColorFormat.YELLOW}Currency addition canceled")

        form.on_submit = on_submit
        form.on_close = on_close
        player.send_form(form)

    def _process_add_currency(self, player: Player, data: str) -> None:
        """Process adding a new currency"""
        try:
            import json
            form_data = json.loads(data)

            if len(form_data) < 3:
                player.send_message(f"{ColorFormat.RED}Invalid form data!")
                return

            item_name = form_data[0].strip()
            points_str = form_data[1].strip()
            display_name = form_data[2].strip()

            # Validate input
            if not item_name:
                player.send_message(f"{ColorFormat.RED}Item name cannot be empty!")
                return

            if not points_str.isdigit():
                player.send_message(f"{ColorFormat.RED}Point value must be a positive number!")
                return

            points = int(points_str)
            if points <= 0:
                player.send_message(f"{ColorFormat.RED}Point value must be greater than 0!")
                return

            if not display_name:
                player.send_message(f"{ColorFormat.RED}Display name cannot be empty!")
                return

            # Check if currency already exists
            if any(c["name"] == item_name for c in self.coin_config):
                player.send_message(f"{ColorFormat.RED}Currency '{item_name}' already exists!")
                return

            # Add new currency
            new_currency = {
                "name": item_name,
                "points": points,
                "display_name": display_name
            }

            self.coin_config.append(new_currency)
            self.coin_config.sort(key=lambda x: x["points"], reverse=True)

            if self._save_currency_config():
                player.send_message(f"{ColorFormat.GREEN}Currency '{item_name}' added successfully!")
                player.send_message(f"{ColorFormat.YELLOW}Value: ${points:,} - {display_name}")
            else:
                player.send_message(f"{ColorFormat.RED}Error saving configuration!")

        except Exception as e:
            player.send_message(f"{ColorFormat.RED}Error adding currency: {e}")

    def _show_edit_currency_form(self, player: Player, currency_name: str) -> None:
        """Show form to edit an existing currency"""
        # Find the currency
        currency = next((c for c in self.coin_config if c["name"] == currency_name), None)
        if not currency:
            player.send_message(f"{ColorFormat.RED}Currency '{currency_name}' not found!")
            return

        form = ModalForm(title=f"{ColorFormat.YELLOW}Edit Currency: {currency_name}")

        form.add_control(TextInput(
            label="Point Value",
            placeholder="100",
            default_value=str(currency["points"])
        ))

        form.add_control(TextInput(
            label="Display Name",
            placeholder="Diamond Coin $100",
            default_value=currency["display_name"]
        ))

        def on_submit(submitter, data: str) -> None:
            self._process_edit_currency(submitter, currency_name, data)

        def on_close(submitter) -> None:
            submitter.send_message(f"{ColorFormat.YELLOW}Currency edit canceled")

        form.on_submit = on_submit
        form.on_close = on_close
        player.send_form(form)

    def _process_edit_currency(self, player: Player, currency_name: str, data: str) -> None:
        """Process editing a currency"""
        try:
            import json
            form_data = json.loads(data)

            if len(form_data) < 2:
                player.send_message(f"{ColorFormat.RED}Invalid form data!")
                return

            points_str = form_data[0].strip()
            display_name = form_data[1].strip()

            # Validate input
            if not points_str.isdigit():
                player.send_message(f"{ColorFormat.RED}Point value must be a positive number!")
                return

            points = int(points_str)
            if points <= 0:
                player.send_message(f"{ColorFormat.RED}Point value must be greater than 0!")
                return

            if not display_name:
                player.send_message(f"{ColorFormat.RED}Display name cannot be empty!")
                return

            # Find and update the currency
            for currency in self.coin_config:
                if currency["name"] == currency_name:
                    currency["points"] = points
                    currency["display_name"] = display_name
                    break
            else:
                player.send_message(f"{ColorFormat.RED}Currency '{currency_name}' not found!")
                return

            # Re-sort by points
            self.coin_config.sort(key=lambda x: x["points"], reverse=True)

            if self._save_currency_config():
                player.send_message(f"{ColorFormat.GREEN}Currency '{currency_name}' updated successfully!")
                player.send_message(f"{ColorFormat.YELLOW}New value: ${points:,} - {display_name}")
            else:
                player.send_message(f"{ColorFormat.RED}Error saving configuration!")

        except Exception as e:
            player.send_message(f"{ColorFormat.RED}Error editing currency: {e}")

    def on_enable(self) -> None:
        """Called when plugin is enabled"""
        self.logger.info("Enabling TK Coins plugin...")

        # Setup configuration paths
        self.config_path = os.path.join(self.data_folder, self.CONFIG_FILE)
        self.messages_path = os.path.join(self.data_folder, self.MESSAGES_FILE)

        # Create data folder if it doesn't exist
        os.makedirs(self.data_folder, exist_ok=True)

        # Load configurations
        self._load_currency_config()
        self._load_messages_config()

        self.register_events(self)
        self._setup_money_objective()
        self.logger.info("=" * 50)
        self.logger.info("TK  Coins plugin enabled successfully!")
        self.logger.info("Features: Money Transfer & Withdrawal System")
        self.logger.info("Scoreboard Objective: Money")
        self.logger.info("=" * 50)

    def on_disable(self) -> None:
        """Called when plugin is disabled"""
        self.logger.info("=" * 50)
        self.logger.info("TK Coins plugin disabled!")
        self.logger.info("=" * 50)

    def _setup_money_objective(self) -> None:
        """Setup the Money scoreboard objective and display it"""
        try:
            scoreboard = self.server.scoreboard

            # List all existing objectives for debugging
            all_objectives = scoreboard.objectives
            self.logger.info(f"Found {len(all_objectives)} existing objectives:")
            for obj in all_objectives:
                self.logger.info(f"  - {obj.name} (display: '{obj.display_name}', criteria: {obj.criteria.name})")

            # Try to get existing objective first
            objective = scoreboard.get_objective(self.MONEY_OBJECTIVE)

            if objective is None:
                # Create new objective if it doesn't exist
                self.logger.info(f"Creating new scoreboard objective: {self.MONEY_OBJECTIVE}")
                objective = scoreboard.add_objective(
                    name=self.MONEY_OBJECTIVE,
                    criteria=Criteria.Type.DUMMY,
                    display_name=f"{ColorFormat.GREEN}Money"
                )
                self.logger.info(f"✓ Created objective '{self.MONEY_OBJECTIVE}' successfully")
            else:
                self.logger.info(f"✓ Found existing objective: {self.MONEY_OBJECTIVE}")


            # Log objective details
            self.logger.info(f"Objective Details:")
            self.logger.info(f"  - Name: {objective.name}")
            self.logger.info(f"  - Display Name: {objective.display_name}")
            self.logger.info(f"  - Criteria: {objective.criteria.name}")
            self.logger.info(f"  - Modifiable: {objective.is_modifiable}")

        except Exception as e:
            self.logger.error(f"✗ Error setting up money objective: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    def get_money(self, player) -> int:
        """Get player's current money score"""
        try:
            self.logger.info(f"Getting money for {player.name}...")

            # Use the player's scoreboard, not the server's scoreboard
            scoreboard = player.scoreboard
            self.logger.info(f"Player scoreboard object: {scoreboard}")

            # List all objectives for debugging
            all_objectives = scoreboard.objectives
            self.logger.info(f"Player's available objectives: {[obj.name for obj in all_objectives]}")

            # Get the Money objective using the official API
            objective = scoreboard.get_objective(self.MONEY_OBJECTIVE)

            if objective is None:
                self.logger.error(f"Money objective '{self.MONEY_OBJECTIVE}' not found on player's scoreboard!")
                self.logger.error(f"Player's available objectives: {[obj.name for obj in all_objectives]}")
                return 0

            self.logger.info(f"Found objective on player's scoreboard: {objective.name} (criteria: {objective.criteria.name})")

            # Get the score using the Player object (as per API documentation)
            score = objective.get_score(player)
            self.logger.info(f"Score object for {player.name}: {score}")

            if score is None:
                self.logger.warning(f"No score found for {player.name} in objective '{objective.name}'")
                return 0

            # Check if score has been set using the correct property
            is_set = score.is_score_set
            self.logger.info(f"Score is_score_set: {is_set}")

            if not is_set:
                self.logger.info(f"Score for {player.name} has not been set yet, returning 0")
                return 0

            # Get the score value
            value = score.value
            self.logger.info(f"Successfully retrieved money for {player.name}: ${value}")
            return value

        except Exception as e:
            self.logger.error(f"Error getting money for {player.name}: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return 0

    def set_money(self, player, amount: int) -> bool:
        """Set player's money score"""
        try:
            # Use the player's scoreboard, not the server's scoreboard
            scoreboard = player.scoreboard
            objective = scoreboard.get_objective(self.MONEY_OBJECTIVE)
            if objective is None:
                self.logger.error("Money objective not found on player's scoreboard")
                return False

            # Use Player object instead of player.name (as per API documentation)
            score = objective.get_score(player)
            score.value = amount
            self.logger.info(f"Set money for {player.name}: ${amount}")
            return True
        except Exception as e:
            self.logger.error(f"Error setting money for {player.name}: {e}")
            return False

    def add_money(self, player, amount: int) -> bool:
        """Add money to player's score"""
        try:
            current = self.get_money(player)
            new_amount = current + amount
            result = self.set_money(player, new_amount)
            if result:
                self.logger.info(f"Added ${amount} to {player.name} (${current} -> ${new_amount})")
            return result
        except Exception as e:
            self.logger.error(f"Error adding money for {player.name}: {e}")
            return False

    @event_handler
    def on_player_interact_actor(self, event: PlayerInteractActorEvent) -> None:
        """Handle player interaction with actors (NPCs)"""
        actor = event.actor
        player = event.player

        # Get actor's scoreboard tags (vanilla tag system)
        tags = actor.scoreboard_tags

        # Check for transfer NPC using vanilla tags
        if "transfer" in tags:
            self.logger.info(f"{player.name} interacted with transfer NPC")
            event.is_cancelled = True  # Cancel default NPC dialogue
            self._handle_transfer_npc(player)
            return

        # Check for withdrawal NPC using vanilla tags
        if "withdrawal" in tags:
            self.logger.info(f"{player.name} interacted with withdrawal NPC")
            event.is_cancelled = True  # Cancel default NPC dialogue
            self._handle_withdrawal_npc(player)
            return

    def _handle_transfer_npc(self, player) -> None:
        """Handle money transfer interaction"""
        money = self.get_money(player)

        # Check if player has enough money
        if money <= 1:
            player.send_message(
                f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('transfer_title')} "
                f"{ColorFormat.DARK_GRAY}] {ColorFormat.RED}{self.get_message('transfer_insufficient', currency_name=self.get_message('currency_name'))}"
            )
            return

        # Get all online players except current player
        online_players = [p for p in self.server.online_players if p.name != player.name]

        if len(online_players) < 1:
            player.send_message(
                f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('transfer_title')} "
                f"{ColorFormat.DARK_GRAY}] {ColorFormat.RED}Nobody is online :({ColorFormat.RESET}"
            )
            return

        # Notify player
        player.send_message(
            f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('transfer_title')} "
            f"{ColorFormat.DARK_GRAY}] {ColorFormat.GOLD}{self.get_message('transfer_prompt')}{ColorFormat.RESET}"
        )

        # Show transfer form
        self._show_transfer_form(player, online_players)

    def _show_transfer_form(self, player, online_players: list) -> None:
        """Show the money transfer form"""
        player_names = [p.name for p in online_players]

        form = ModalForm(
            title=f"{ColorFormat.RESET}{ColorFormat.GREEN}{self.get_message('transfer_title')}{ColorFormat.RESET}"
        )

        form.add_control(Dropdown(
            label="Choose a player",
            options=player_names
        ))

        form.add_control(TextInput(
            label="How many coins do you want to transfer?",
            placeholder="Enter a number (e.g., 10)",
            default_value="0"
        ))

        form.add_control(TextInput(
            label="Message",
            placeholder="Thanks!",
            default_value="Thanks!"
        ))

        def on_submit(submitter, data: str) -> None:
            self._process_transfer(submitter, online_players, data)

        def on_close(submitter) -> None:
            submitter.send_message(
                f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('transfer_title')} "
                f"{ColorFormat.DARK_GRAY}] {ColorFormat.RED}{self.get_message('transfer_canceled')}{ColorFormat.RESET}"
            )
            self.logger.info(f"{submitter.name} canceled money transfer")

        form.on_submit = on_submit
        form.on_close = on_close
        player.send_form(form)

    def _process_transfer(self, player, online_players: list, data: str) -> None:
        """Process the money transfer"""
        try:
            import json
            form_data = json.loads(data)

            # Get form values - form_data is a list, not a dict
            selected_index = int(form_data[0]) if form_data and len(form_data) > 0 else 0
            transfer_amount_str = form_data[1] if len(form_data) > 1 else "0"
            message = form_data[2] if len(form_data) > 2 else ""

            # Validate number input
            if not transfer_amount_str.isdigit():
                player.send_message(
                    f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('transfer_title')} "
                    f"{ColorFormat.DARK_GRAY}] {ColorFormat.RED}{self.get_message('invalid_amount')}{ColorFormat.RESET}"
                )
                return

            transfer_amount = int(transfer_amount_str)

            # Validate transfer amount
            if transfer_amount <= 0:
                player.send_message(
                    f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('transfer_title')} "
                    f"{ColorFormat.DARK_GRAY}] {ColorFormat.RED}{self.get_message('amount_too_low')}{ColorFormat.RESET}"
                )
                return

            # Check if player has enough money
            current_money = self.get_money(player)
            if transfer_amount > current_money:
                player.send_message(
                    f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('transfer_title')} "
                    f"{ColorFormat.DARK_GRAY}] {ColorFormat.RED}{self.get_message('amount_too_high')}{ColorFormat.RESET}"
                )
                return

            # Get selected player
            if selected_index >= len(online_players):
                player.send_message(
                    f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('transfer_title')} "
                    f"{ColorFormat.DARK_GRAY}] {ColorFormat.RED}{self.get_message('player_not_found')}{ColorFormat.RESET}"
                )
                return

            target_player = online_players[selected_index]

            # Verify target player is still online
            if target_player not in self.server.online_players:
                player.send_message(
                    f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('transfer_title')} "
                    f"{ColorFormat.DARK_GRAY}] {ColorFormat.RED}{self.get_message('player_not_found')}{ColorFormat.RESET}"
                )
                return

            # Perform transfer
            if self.add_money(player, -transfer_amount) and self.add_money(target_player, transfer_amount):
                # Add greeting if provided
                greeting = f" {ColorFormat.AQUA}with this greeting: {message}" if message else ""

                # Notify sender
                player.send_message(
                    f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('transfer_title')} "
                    f"{ColorFormat.DARK_GRAY}] {ColorFormat.AQUA}{self.get_message('transfer_success', amount=transfer_amount, player=target_player.name)} "
                    f"{ColorFormat.RESET}{ColorFormat.AQUA}{greeting}."
                )

                # Notify receiver
                target_player.send_message(
                    f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('transfer_title')} "
                    f"{ColorFormat.DARK_GRAY}] {ColorFormat.AQUA}{self.get_message('transfer_received', amount=transfer_amount, player=player.name)} "
                    f"{ColorFormat.RESET}{ColorFormat.AQUA}{greeting}{ColorFormat.RESET}"
                )

                # Log to console
                self.logger.info(f"✓ Transfer complete: {player.name} -> {target_player.name}: ${transfer_amount}")
            else:
                player.send_message(
                    f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('transfer_title')} "
                    f"{ColorFormat.DARK_GRAY}] {ColorFormat.RED}{self.get_message('error_processing')}{ColorFormat.RESET}"
                )
                self.logger.error(f"✗ Transfer failed: {player.name} -> {target_player.name}: ${transfer_amount}")

        except Exception as e:
            self.logger.error(f"Error processing transfer: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            player.send_message(
                f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('transfer_title')} "
                f"{ColorFormat.DARK_GRAY}] {ColorFormat.RED}{self.get_message('error_processing')}{ColorFormat.RESET}"
            )

    def _handle_withdrawal_npc(self, player) -> None:
        """Handle money withdrawal interaction"""
        money = self.get_money(player)

        if money <= 0:
            player.send_message(
                f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('withdrawal_title')} "
                f"{ColorFormat.DARK_GRAY}] {ColorFormat.RED}{self.get_message('withdrawal_insufficient', currency_name=self.get_message('currency_name'))}"
            )
            return

        player.send_message(
            f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('withdrawal_title')} "
            f"{ColorFormat.DARK_GRAY}] {ColorFormat.GOLD}{self.get_message('withdrawal_prompt')} "
        )

        # Show withdrawal form
        self._show_withdrawal_form(player)

    def _show_withdrawal_form(self, player) -> None:
        """Show the money withdrawal form"""
        form = ModalForm(
            title=f"{ColorFormat.RESET}{ColorFormat.GREEN}{self.get_message('withdrawal_title')}{ColorFormat.RESET}"
        )

        form.add_control(TextInput(
            label="How much money do you want to withdraw?",
            placeholder="Enter a number",
            default_value="0"
        ))

        def on_submit(submitter, data: str) -> None:
            self._process_withdrawal(submitter, data)

        def on_close(submitter) -> None:
            submitter.send_message(
                f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('withdrawal_title')} "
                f"{ColorFormat.DARK_GRAY}] {ColorFormat.RED}{self.get_message('withdrawal_canceled')}"
            )
            self.logger.info(f"{submitter.name} canceled withdrawal")

        form.on_submit = on_submit
        form.on_close = on_close
        player.send_form(form)

    def _process_withdrawal(self, player, data: str) -> None:
        """Process the money withdrawal"""
        try:
            import json
            form_data = json.loads(data)

            # Get withdrawal amount - form_data is a list, not a dict
            withdrawal_str = form_data[0] if form_data and len(form_data) > 0 else "0"

            # Validate input
            if not withdrawal_str.isdigit():
                player.send_message(
                    f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('withdrawal_title')} "
                    f"{ColorFormat.DARK_GRAY}] {ColorFormat.RED}{self.get_message('invalid_amount')}"
                )
                return

            requested_withdrawal = int(withdrawal_str)

            if requested_withdrawal <= 0:
                player.send_message(
                    f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}Money "
                    f"{ColorFormat.YELLOW}Withdrawal{ColorFormat.DARK_GRAY}] {ColorFormat.RED}Please enter a number greater than 0!"
                )
                return

            current_money = self.get_money(player)

            if requested_withdrawal > current_money:
                player.send_message(
                    f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}Money "
                    f"{ColorFormat.YELLOW}Withdrawal{ColorFormat.DARK_GRAY}] {ColorFormat.RED}You do not have enough TK Coins!"
                )
                return

            # Calculate maximum withdrawable amount based on inventory space
            max_fit = self._get_max_withdrawable(player, requested_withdrawal)

            actual_withdrawal = requested_withdrawal
            if requested_withdrawal > max_fit:
                actual_withdrawal = max_fit
                player.send_message(
                    f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('withdrawal_title')} "
                    f"{ColorFormat.DARK_GRAY}] {ColorFormat.YELLOW}{self.get_message('inventory_full', max_amount=max_fit)}"
                )

            # Deduct money
            if not self.add_money(player, -actual_withdrawal):
                player.send_message(
                    f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('withdrawal_title')} "
                    f"{ColorFormat.DARK_GRAY}] {ColorFormat.RED}{self.get_message('error_processing')}"
                )
                return

            # Calculate coin distribution
            coin_distribution = self._calculate_coin_distribution(actual_withdrawal)

            # Give coins to player
            withdrawal_message = f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}{self.get_message('withdrawal_title')}{ColorFormat.DARK_GRAY}] {ColorFormat.GREEN}{self.get_message('withdrawal_success')} "
            first_item = True

            for coin_name, count in coin_distribution:
                if count > 0:
                    # Give coins in stacks
                    remaining = count
                    while remaining > 0:
                        stack_size = min(remaining, 64)
                        player.perform_command(f"give @s {coin_name} {stack_size}")
                        remaining -= stack_size

                    # Find display name
                    coin_info = next((c for c in self.coin_config if c["name"] == coin_name), None)
                    if coin_info:
                        if not first_item:
                            withdrawal_message += ", "
                        withdrawal_message += f"{count} {coin_info['display_name']}"
                        first_item = False

            player.send_message(withdrawal_message)
            self.logger.info(f"✓ Withdrawal complete: {player.name} withdrew ${actual_withdrawal}")

        except Exception as e:
            self.logger.error(f"Error processing withdrawal: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

            # Refund money on error
            try:
                import json
                form_data = json.loads(data)
                withdrawal_str = form_data[0] if form_data and len(form_data) > 0 else "0"
                if withdrawal_str.isdigit():
                    refund_amount = int(withdrawal_str)
                    self.add_money(player, refund_amount)
                    self.logger.info(f"Refunded ${refund_amount} to {player.name} due to error")
            except:
                pass

            player.send_message(
                f"{ColorFormat.RESET}{ColorFormat.DARK_GRAY}[{ColorFormat.GREEN}Money "
                f"{ColorFormat.YELLOW}Withdrawal{ColorFormat.DARK_GRAY}] {ColorFormat.RED}Error processing withdrawal!"
            )

    def _get_empty_slots(self, player) -> int:
        """Get the number of empty slots in player's inventory"""
        try:
            inventory = player.inventory
            empty_slots = 0

            for i in range(inventory.size):
                if inventory.get_item(i) is None:
                    empty_slots += 1

            return empty_slots
        except Exception as e:
            self.logger.error(f"Error getting empty slots: {e}")
            return 0

    def _get_max_withdrawable(self, player, amount: int) -> int:
        """Calculate maximum withdrawable amount based on inventory space"""
        try:
            inventory = player.inventory
            max_stack = 64
            available_space = self._get_empty_slots(player) * max_stack

            # Check existing coin stacks that aren't full
            for i in range(inventory.size):
                item = inventory.get_item(i)
                if item and any(coin["name"] == item.type for coin in self.coin_config):
                    available_space += max_stack - item.amount

            # Calculate how many coins are needed
            coin_distribution = self._calculate_coin_distribution(amount)
            total_coins_needed = sum(count for _, count in coin_distribution)

            if total_coins_needed == 0:
                return amount

            # Adjust based on inventory space
            return min(amount, int(available_space / total_coins_needed * amount))
        except Exception as e:
            self.logger.error(f"Error calculating max withdrawable: {e}")
            return 0

    def _calculate_coin_distribution(self, amount: int) -> list:
        """Calculate optimal coin distribution for given amount"""
        remaining = amount
        distribution = []

        for coin in self.coin_config:
            coin_count = remaining // coin["points"]
            if coin_count > 0:
                distribution.append((coin["name"], coin_count))
                remaining -= coin_count * coin["points"]

        return distribution