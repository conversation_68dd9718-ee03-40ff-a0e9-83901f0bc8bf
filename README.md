# OG Coins - Endstone Plugin

A comprehensive money system plugin for Endstone/Minecraft Bedrock servers featuring money transfers and physical coin withdrawals.

## Features

- **Money Transfer System**: Players can transfer money to other online players through an NPC
- **Money Withdrawal System**: Players can withdraw their virtual money as physical coins
- **Multi-tier Coin System**: 9 different coin types from wooden ($1) to netherite ($1,000,000)
- **Inventory Management**: Smart inventory checking to prevent overflow
- **Scoreboard Integration**: Uses Minecraft scoreboard to track player money

## Installation

1. Ensure you have Endstone 0.10.6 or higher installed
2. Place the plugin in your Endstone plugins directory
3. Restart your server

## Directory Structure

```
endstone-og-coins/
├── pyproject.toml
├── README.md
└── src/
    └── endstone_og_coins/
        ├── __init__.py
        └── og_coins.py
```

## Configuration

### Coin Types (in order of value):

- Netherite OG Coin: $1,000,000
- Diamond OG Coin: $10,000
- Golden OG Coin: $1,000
- Ruby OG Coin: $500
- Emerald OG Coin: $100
- Iron OG Coin: $25
- Glass OG Coin: $5
- Wooden OG Coin: $1
- OG Coins (zombie:zcoin): $1

## Usage

### For Players

**Money Transfer:**
1. Interact with an NPC tagged with "transfer"
2. Select the player you want to send money to
3. Enter the amount to transfer
4. Optionally add a message
5. Confirm the transfer

**Money Withdrawal:**
1. Interact with an NPC tagged with "withdrawal"
2. Enter the amount you want to withdraw
3. Receive physical coins in your inventory
4. The system automatically calculates the optimal coin distribution

### For Server Administrators

**Setting up Transfer NPC:**
```
/tag @e[type=npc,name="Transfer NPC"] add transfer
```

**Setting up Withdrawal NPC:**
```
/tag @e[type=npc,name="Withdrawal NPC"] add withdrawal
```

**Note:** The plugin uses Minecraft's vanilla scoreboard tag system. Tags are stored in `actor.scoreboard_tags`.

**Managing Player Money:**
```
/scoreboard players set <player> Money <amount>
/scoreboard players add <player> Money <amount>
/scoreboard players remove <player> Money <amount>
```

## Console Output

The plugin provides detailed console logging:

**On Plugin Enable:**
```
==================================================
OG Coins Plugin Loading...
==================================================
Enabling OG Coins plugin...
Creating new scoreboard objective: Money
✓ Created objective 'Money' successfully
✓ Displaying 'Money' objective in sidebar (descending order)
Objective Details:
  - Name: Money
  - Display Name: Money
  - Criteria: dummy
  - Modifiable: True
==================================================
OG Coins plugin enabled successfully!
Features: Money Transfer & Withdrawal System
Scoreboard Objective: Money
==================================================
```

**During Operations:**
```
PlayerName interacted with transfer NPC
Set money for PlayerName: $950
Added $-50 to PlayerName ($1000 -> $950)
Set money for TargetPlayer: $1050
Added $50 to TargetPlayer ($1000 -> $1050)
✓ Transfer complete: PlayerName -> TargetPlayer: $50
```

**On Plugin Disable:**
```
==================================================
OG Coins plugin disabled!
==================================================
```

## Requirements

- Python 3.9+

## API Usage

The plugin exposes several useful methods:

```python
# Get player's money
money = plugin.get_money(player)

# Set player's money
plugin.set_money(player, 1000)

# Add money to player
plugin.add_money(player, 500)

# Calculate coin distribution
distribution = plugin._calculate_coin_distribution(1575)
# Returns: [("ninjos:coin_gold", 1), ("ninjos:coin_ruby", 1), ("ninjos:coin_iron", 3)]
```

## Technical Details

- Uses Minecraft's **vanilla scoreboard system** for persistent money storage
- Uses **vanilla scoreboard tags** (`actor.scoreboard_tags`) to identify NPCs
- Automatically creates and displays the "Money" objective in the sidebar
- Implements smart inventory management to prevent coin overflow
- Validates all user inputs to prevent exploits
- Comprehensive error handling and console logging
- Compatible with Endstone API 0.10.6+

## Differences from Original JavaScript Version

This Endstone Python port maintains full feature parity with the original Minecraft Bedrock script while adapting to the Endstone API:

- Event system uses Endstone's `@event_handler` decorator
- Forms use Endstone's form API instead of `@minecraft/server-ui`
- Scoreboard operations use Endstone's scoreboard API
- Color formatting uses `ColorFormat` enum instead of section signs
- Commands use `player.perform_command()` instead of `runCommandAsync()`

## License

This plugin is provided as-is for use with Endstone servers.

## Support

For issues or questions, please refer to the Endstone documentation:
- https://endstone.dev/latest/